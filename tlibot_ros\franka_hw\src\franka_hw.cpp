
// Copyright (c) 2017 Franka Emika GmbH
// Use of this source code is governed by the Apache-2.0 license, see LICENSE
#include <franka_hw/franka_hw.h>
#include <franka_hw/resource_helpers.h>

#include <array>
#include <cstdint>
#include <exception>
#include <functional>
#include <list>
#include <mutex>
#include <ostream>
#include <sstream>
#include <string>
#include <utility>

#include <franka/control_types.h>
#include <franka/rate_limiting.h>
#include <franka/robot.h>
#include <joint_limits_interface/joint_limits_urdf.h>
#include <cmath>
#include <Eigen/Dense>
#include <Eigen/Geometry>

namespace franka_hw {

namespace {
std::ostream& operator<<(std::ostream& ostream, franka::ControllerMode mode) {
  if (mode == franka::ControllerMode::kJointImpedance) {
    ostream << "joint_impedance";
  } else if (mode == franka::ControllerMode::kCartesianImpedance) {
    ostream << "cartesian_impedance";
  } else {
    ostream << "<unknown>";
  }
  return ostream;
}

std::string toStringWithPrecision(const double value, const size_t precision = 6) {
  std::ostringstream out;
  out.precision(precision);
  out << std::fixed << value;
  return out.str();
}

}  // anonymous namespace

FrankaHW::FrankaHW()
    : position_joint_command_ros_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      position_joint_command_libfranka_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      velocity_joint_command_ros_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      velocity_joint_command_libfranka_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      effort_joint_command_ros_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      effort_joint_command_libfranka_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      pose_cartesian_command_ros_(
          {1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0}),
      pose_cartesian_command_libfranka_(
          {1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0}),
      velocity_cartesian_command_ros_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      velocity_cartesian_command_libfranka_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      previous_joint_positions_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}),
      previous_read_time_(0.0),
      first_read_(true),
      velocity_filter_alpha_(0.1),
      filtered_velocities_({0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0}) {}

bool FrankaHW::init(ros::NodeHandle& root_nh, ros::NodeHandle& robot_hw_nh) {
  if (initialized_) {
    ROS_ERROR("FrankaHW: Cannot be initialized twice.");
    return false;
  }

  if (!initParameters(root_nh, robot_hw_nh)) {
    ROS_ERROR("FrankaHW: Failed to parse all required parameters.");
    return false;
  }
  try {
    initRobot();
  } catch (const std::runtime_error& error) {
    ROS_ERROR("FrankaHW: Failed to initialize libfranka robot. %s", error.what());
    return false;
  }
  initROSInterfaces(robot_hw_nh);
  setupParameterCallbacks(robot_hw_nh);

  initialized_ = true;
  return true;
}

bool FrankaHW::initParameters(ros::NodeHandle& root_nh, ros::NodeHandle& robot_hw_nh) {
  std::vector<std::string> joint_names_vector;
  if (!robot_hw_nh.getParam("joint_names", joint_names_vector) || joint_names_vector.size() != 7) {
    ROS_ERROR("Invalid or no joint_names parameters provided");
    return false;
  }
  std::copy(joint_names_vector.cbegin(), joint_names_vector.cend(), joint_names_.begin());

  bool rate_limiting;
  if (!robot_hw_nh.getParamCached("rate_limiting", rate_limiting)) {
    ROS_ERROR("Invalid or no rate_limiting parameter provided");
    return false;
  }

  double cutoff_frequency;
  if (!robot_hw_nh.getParamCached("cutoff_frequency", cutoff_frequency)) {
    ROS_ERROR("Invalid or no cutoff_frequency parameter provided");
    return false;
  }

  std::string internal_controller;
  if (!robot_hw_nh.getParam("internal_controller", internal_controller)) {
    ROS_ERROR("No internal_controller parameter provided");
    return false;
  }

  if (!robot_hw_nh.getParam("arm_id", arm_id_)) {
    ROS_ERROR("Invalid or no arm_id parameter provided");
    return false;
  }

  if (!urdf_model_.initParamWithNodeHandle("robot_description", root_nh)) {
    ROS_ERROR("Could not initialize URDF model from robot_description");
    return false;
  }

  if (!robot_hw_nh.getParam("robot_ip", robot_ip_)) {
    ROS_ERROR("Invalid or no robot_ip parameter provided");
    return false;
  }

  if (!robot_hw_nh.getParam("robot_port", robot_port_)) {
    ROS_INFO("No robot_port parameter provided, use default port 6001");
    robot_port_ = 6001;
  }

  if (!robot_hw_nh.getParam("joint_limit_warning_threshold", joint_limit_warning_threshold_)) {
    ROS_INFO(
        "No parameter joint_limit_warning_threshold is found, using default "
        "value %f",
        joint_limit_warning_threshold_);
  }

  std::string realtime_config_param = robot_hw_nh.param("realtime_config", std::string("enforce"));
  if (realtime_config_param == "enforce") {
    realtime_config_ = franka::RealtimeConfig::kEnforce;
  } else if (realtime_config_param == "ignore") {
    realtime_config_ = franka::RealtimeConfig::kIgnore;
  } else {
    ROS_ERROR("Invalid realtime_config parameter provided. Valid values are 'enforce', 'ignore'.");
    return false;
  }

  // 读取速度滤波器参数
  if (!robot_hw_nh.getParam("velocity_filter_alpha", velocity_filter_alpha_)) {
    ROS_INFO("No velocity_filter_alpha parameter provided, using default value %f", velocity_filter_alpha_);
  }
  
  // 验证参数范围
  if (velocity_filter_alpha_ <= 0.0 || velocity_filter_alpha_ > 1.0) {
    ROS_WARN("velocity_filter_alpha out of range (0, 1], using default value 0.1");
    velocity_filter_alpha_ = 0.1;
  }

  // Get full collision behavior config from the parameter server.
  std::vector<double> thresholds =
      getCollisionThresholds("lower_torque_thresholds_acceleration", robot_hw_nh,
                             {20.0, 20.0, 18.0, 18.0, 16.0, 14.0, 12.0});
  std::copy(thresholds.begin(), thresholds.end(),
            collision_config_.lower_torque_thresholds_acceleration.begin());
  thresholds = getCollisionThresholds("upper_torque_thresholds_acceleration", robot_hw_nh,
                                      {20.0, 20.0, 18.0, 18.0, 16.0, 14.0, 12.0});
  std::copy(thresholds.begin(), thresholds.end(),
            collision_config_.upper_torque_thresholds_acceleration.begin());
  thresholds = getCollisionThresholds("lower_torque_thresholds_nominal", robot_hw_nh,
                                      {20.0, 20.0, 18.0, 18.0, 16.0, 14.0, 12.0});
  std::copy(thresholds.begin(), thresholds.end(),
            collision_config_.lower_torque_thresholds_nominal.begin());
  thresholds = getCollisionThresholds("upper_torque_thresholds_nominal", robot_hw_nh,
                                      {20.0, 20.0, 18.0, 18.0, 16.0, 14.0, 12.0});
  std::copy(thresholds.begin(), thresholds.end(),
            collision_config_.upper_torque_thresholds_nominal.begin());
  thresholds.resize(6);
  thresholds = getCollisionThresholds("lower_force_thresholds_acceleration", robot_hw_nh,
                                      {20.0, 20.0, 20.0, 25.0, 25.0, 25.0});
  std::copy(thresholds.begin(), thresholds.end(),
            collision_config_.lower_force_thresholds_acceleration.begin());
  thresholds = getCollisionThresholds("upper_force_thresholds_acceleration", robot_hw_nh,
                                      {20.0, 20.0, 20.0, 25.0, 25.0, 25.0});
  std::copy(thresholds.begin(), thresholds.end(),
            collision_config_.upper_force_thresholds_acceleration.begin());
  thresholds = getCollisionThresholds("lower_force_thresholds_nominal", robot_hw_nh,
                                      {20.0, 20.0, 20.0, 25.0, 25.0, 25.0});
  std::copy(thresholds.begin(), thresholds.end(),
            collision_config_.lower_force_thresholds_nominal.begin());
  thresholds = getCollisionThresholds("upper_force_thresholds_nominal", robot_hw_nh,
                                      {20.0, 20.0, 20.0, 25.0, 25.0, 25.0});
  std::copy(thresholds.begin(), thresholds.end(),
            collision_config_.upper_force_thresholds_nominal.begin());

  return true;
}

void FrankaHW::connect()
{
    std::lock_guard<std::mutex> lock(robot_mutex_);
    if (!tlibot_)
    {
        tlibot_ = std::make_unique<tcb710_sdk::RobotArmSDK>(robot_ip_, robot_port_);
        if (tlibot_)
        {
            tlibot_->connect();
        }
        // tlibot_->setCollisionBehavior(collision_config_.lower_torque_thresholds_acceleration,
        //                              collision_config_.upper_torque_thresholds_acceleration,
        //                              collision_config_.lower_torque_thresholds_nominal,
        //                              collision_config_.upper_torque_thresholds_nominal,
        //                              collision_config_.lower_force_thresholds_acceleration,
        //                              collision_config_.upper_force_thresholds_acceleration,
        //                              collision_config_.lower_force_thresholds_nominal,
        //                              collision_config_.upper_force_thresholds_nominal);
    }
}

bool FrankaHW::disconnect()
{
    if (controllerActive())
    {
        ROS_ERROR("FrankaHW: Rejected attempt to disconnect while controller is still running!");
        return false;
    }
    std::lock_guard<std::mutex> lock(robot_mutex_);
    if (tlibot_)
    {
        tlibot_->disconnect();
    }
    tlibot_.reset();
    return true;
}

bool FrankaHW::connected()
{
    return tlibot_ != nullptr && tlibot_->isConnected();
}

void FrankaHW::update(const franka::RobotState& robot_state) {
//   std::lock_guard<std::mutex> ros_lock(ros_state_mutex_);
//   robot_state_ros_ = robot_state;
}

bool FrankaHW::controllerActive() const noexcept {
  return controller_active_;
}

std::mutex& FrankaHW::robotMutex() {
  return robot_mutex_;
}

void FrankaHW::control(
    const std::function<bool(const ros::Time&, const ros::Duration&)>& ros_callback) {
  if (!initialized_) {
    ROS_ERROR("FrankaHW: Call to control before initialization!");
    return;
  }
  if (!controller_active_) {
    return;
  }

  franka::Duration last_time = robot_state_ros_.time;

//   std::lock_guard<std::mutex> lock(robot_mutex_);
//   run_function_(*robot_, [this, ros_callback, &last_time](const franka::RobotState& robot_state,
//                                                           franka::Duration time_step) {
//     if (last_time != robot_state.time) {
//       last_time = robot_state.time;

//       return ros_callback(ros::Time::now(), ros::Duration(time_step.toSec()));
//     }
//     return true;
//   });
}

void FrankaHW::enforceLimits(const ros::Duration& period) {
  if (period.toSec() > 0.0) {
    position_joint_limit_interface_.enforceLimits(period);
    velocity_joint_limit_interface_.enforceLimits(period);
    effort_joint_limit_interface_.enforceLimits(period);
  }
}

bool FrankaHW::checkForConflict(const std::list<hardware_interface::ControllerInfo>& info) const {
  ResourceWithClaimsMap resource_map = getResourceMap(info);
  if (hasConflictingMultiClaim(resource_map)) {
    return true;
  }
  ArmClaimedMap arm_claim_map;
  if (!getArmClaimedMap(resource_map, arm_claim_map)) {
    ROS_ERROR_STREAM("FrankaHW: Unknown interface claimed. Conflict!");
    return true;
  }
  return hasConflictingJointAndCartesianClaim(arm_claim_map, arm_id_) ||
         partiallyClaimsArmJoints(arm_claim_map, arm_id_);
}

// doSwitch runs on the main realtime thread.
void FrankaHW::doSwitch(const std::list<hardware_interface::ControllerInfo>& /* start_list */,
                        const std::list<hardware_interface::ControllerInfo>& /* stop_list */) {
  if (current_control_mode_ != ControlMode::None) {
    reset();
    controller_active_ = true;
  }
}

// prepareSwitch runs on the background message handling thread.
bool FrankaHW::prepareSwitch(const std::list<hardware_interface::ControllerInfo>& start_list,
                             const std::list<hardware_interface::ControllerInfo>& stop_list) {
  ResourceWithClaimsMap start_resource_map = getResourceMap(start_list);
  ArmClaimedMap start_arm_claim_map;
  if (!getArmClaimedMap(start_resource_map, start_arm_claim_map)) {
    ROS_ERROR("FrankaHW: Unknown interface claimed for starting!");
    return false;
  }

  ControlMode start_control_mode = getControlMode(arm_id_, start_arm_claim_map);

  ResourceWithClaimsMap stop_resource_map = getResourceMap(stop_list);
  ArmClaimedMap stop_arm_claim_map;
  if (!getArmClaimedMap(stop_resource_map, stop_arm_claim_map)) {
    ROS_ERROR("FrankaHW: Unknown interface claimed for stopping!");
    return false;
  }
  ControlMode stop_control_mode = getControlMode(arm_id_, stop_arm_claim_map);

  ControlMode requested_control_mode = current_control_mode_;
  requested_control_mode &= ~stop_control_mode;
  requested_control_mode |= start_control_mode;

  if (!setRunFunction(requested_control_mode, get_limit_rate_(), get_cutoff_frequency_(),
                      get_internal_controller_())) {
    return false;
  }

  if (current_control_mode_ != requested_control_mode) {
    ROS_INFO_STREAM("FrankaHW: Prepared switching controllers to "
                    << requested_control_mode << " with parameters "
                    << "limit_rate=" << get_limit_rate_()
                    << ", cutoff_frequency=" << get_cutoff_frequency_()
                    << ", internal_controller=" << get_internal_controller_());
    current_control_mode_ = requested_control_mode;
    controller_active_ = false;
  }

  return true;
}

std::array<double, 7> FrankaHW::getJointPositionCommand() const noexcept {
  return position_joint_command_ros_.q;
}

std::array<double, 7> FrankaHW::getJointVelocityCommand() const noexcept {
  return velocity_joint_command_ros_.dq;
}

std::array<double, 7> FrankaHW::getJointEffortCommand() const noexcept {
  return effort_joint_command_ros_.tau_J;
}

void FrankaHW::reset() {
  position_joint_limit_interface_.reset();
}

void FrankaHW::checkJointLimits() {
  std::string joint_limits_warning;
  for (const auto& k_joint_name : joint_names_) {
    try {
      auto joint_handle = joint_state_interface_.getHandle(k_joint_name);
      auto urdf_joint = urdf_model_.getJoint(k_joint_name);
      joint_limits_interface::JointLimits joint_limits;
      if (getJointLimits(urdf_joint, joint_limits)) {
        double joint_lower = joint_limits.min_position;
        double joint_upper = joint_limits.max_position;
        double joint_position = joint_handle.getPosition();
        double dist = fmin(fabs(joint_position - joint_lower), fabs(joint_position - joint_upper));
        if (dist < joint_limit_warning_threshold_) {
          joint_limits_warning +=
              "\n\t" + k_joint_name + ": " + toStringWithPrecision(dist * 180 / M_PI) +
              " degrees to joint limits (limits: [" + toStringWithPrecision(joint_lower) + ", " +
              toStringWithPrecision(joint_upper) + "]" +
              " q: " + toStringWithPrecision(joint_position) + ") ";
        }
      } else {
        ROS_ERROR_STREAM_ONCE("FrankaHW: Could not parse joint limit for joint "
                              << k_joint_name << " for joint limit interfaces");
      }
    } catch (const hardware_interface::HardwareInterfaceException& ex) {
      ROS_ERROR_STREAM_ONCE("FrankaHW::checkJointLimits Could not get joint handle " << k_joint_name
                                                                                     << " .\n"
                                                                                     << ex.what());
      return;
    }
  }
  if (!joint_limits_warning.empty()) {
    ROS_WARN_THROTTLE(5, "FrankaHW: %s", joint_limits_warning.c_str());
  }
}

bool FrankaHW::isFdr2Robot() const {
  // Check if arm_id contains "fdr2" or if joint names match FDR2 pattern
  if (arm_id_.find("fdr2") != std::string::npos || arm_id_.find("FDR2") != std::string::npos) {
    return true;
  }

  // Check if all joint names match the FDR2 pattern (joint1, joint2, ..., joint7)
  bool matches_fdr2_pattern = true;
  for (size_t i = 0; i < joint_names_.size(); i++) {
    std::string expected_name = "joint" + std::to_string(i + 1);
    if (joint_names_[i] != expected_name) {
      matches_fdr2_pattern = false;
      break;
    }
  }

  return matches_fdr2_pattern;
}

franka::Robot& FrankaHW::robot() const {
  if (!initialized_ || !robot_) {
    std::string error_message = !initialized_
                                    ? "FrankaHW: Attempt to access robot before initialization!"
                                    : "FrankaHW: Attempt to access disconnected robot!";
    throw std::logic_error(error_message);
  }
  return *robot_;
}

std::array<double, 16> poseToTransformArray(const std::array<double, 7>& q)
{
    // 1. 提取位置
    // x, y, z 是 mm，需要转换为米 (m)
    const double inv1000 = 0.001;
    const double x = q[0] * inv1000; // x (m)
    const double y = q[1] * inv1000; // y (m)
    const double z = q[2] * inv1000; // z (m)
    
    // 2. 提取欧拉角 (a, b, c 对应 Yaw, Pitch, Roll)
    const double yaw = q[3];   // a (rad)
    const double pitch = q[4]; // b (rad)
    const double roll = q[5];  // c (rad)

    // 3. 使用 Eigen::Affine3d 构建变换矩阵
    Eigen::Affine3d transform = Eigen::Affine3d::Identity();

    // 构建旋转：ZYX 欧拉角 (Yaw, Pitch, Roll)
    // Eigen 的 AngleAxis 顺序很重要：先乘的旋转轴是相对于当前坐标系旋转
    // 或者说，用 ZYX 欧拉角来组合旋转时，通常是：
    // R = R_Z(yaw) * R_Y(pitch) * R_X(roll)
    // 在 Eigen 中，这对应于：
    Eigen::Quaterniond quat = Eigen::AngleAxisd(yaw,   Eigen::Vector3d::UnitZ()) *
                              Eigen::AngleAxisd(pitch, Eigen::Vector3d::UnitY()) *
                              Eigen::AngleAxisd(roll,  Eigen::Vector3d::UnitX());
    // 重要的是外层乘法顺序，确保对应 ZYX。
    // 如果是内层乘法（Intrinsic Rotation，绕着自身坐标轴旋转），顺序会相反
    // 但对于外层乘法（Extrinsic Rotation，绕着固定坐标轴旋转），是 R_Z * R_Y * R_X。
    // Eigen 的 AngleAxis 链式乘法是按照右乘（R_new = R_old * R_step）来组合的
    // 所以，AngleAxis(Z) * AngleAxis(Y) * AngleAxis(X) 正好对应 ZYX 欧拉角的复合旋转。
    transform.rotate(quat);

    // 设置平移 (已经在步骤1转换为米)
    transform.translation() << x, y, z;

    // 4. 将 Eigen::Matrix4d 转换为 std::array<double, 16>
    std::array<double, 16> result_array;
    Eigen::Map<Eigen::Matrix4d>(result_array.data()) = transform.matrix();

    return result_array;
}

std::array<double, 7> transformArrayToPose(const std::array<double, 16>& transform_array)
{
    std::array<double, 7> q;

    // 1. 将 std::array<double, 16> 映射/转换为 Eigen::Affine3d
    Eigen::Map<const Eigen::Matrix4d> eigen_matrix(transform_array.data());
    Eigen::Affine3d transform(eigen_matrix);

    // 2. 提取位置 (Translation)
    // 从米 (m) 转换为毫米 (mm)
    q[0] = transform.translation().x() * 1000.0; 
    q[1] = transform.translation().y() * 1000.0;
    q[2] = transform.translation().z() * 1000.0;

    // 3. 提取欧拉角 (Yaw, Pitch, Roll)
    Eigen::Quaterniond orientation_quat(transform.rotation());

    // 使用 Eigen 的 .toRotationMatrix().eulerAngles() 方法
    // eulerAngles(2, 1, 0) 表示 ZYX 顺序 (Z轴 -> Y轴 -> X轴)
    // 返回的向量中：
    // euler_angles[0] 是 Z 轴旋转 (Yaw)
    // euler_angles[1] 是 Y 轴旋转 (Pitch)
    // euler_angles[2] 是 X 轴旋转 (Roll)
    Eigen::Vector3d euler_angles = orientation_quat.toRotationMatrix().eulerAngles(2, 1, 0); 
    
    // 将提取出的欧拉角赋值给 q[3], q[4], q[5]
    // a (q[3]) = Yaw
    // b (q[4]) = Pitch
    // c (q[5]) = Roll
    q[3] = euler_angles[0]; // a (Yaw)
    q[4] = euler_angles[1]; // b (Pitch)
    q[5] = euler_angles[2]; // c (Roll)

    // q[6] 未使用
    q[6] = 0.0;

    return q;
}


void FrankaHW::read(const ros::Time& time, const ros::Duration& period) {
    std::lock_guard<std::mutex> ros_lock(ros_state_mutex_);
    
    try {
        // 1. 读取关节位置
        std::array<double, 7> current_positions = tlibot_->currentPosInquiry(tcb710_sdk::CoordinateType::JOINT, robot_id_);
        robot_state_ros_.q = current_positions;
        
        // 2. 读取笛卡尔位姿
        robot_state_ros_.O_T_EE = poseToTransformArray(tlibot_->currentPosInquiry(tcb710_sdk::CoordinateType::CART, robot_id_));
        
        // 3. 读取速度信息（新增）
        auto velocity_data = tlibot_->axisActualVelInquire(robot_id_);
        double actualLineVel = std::get<0>(velocity_data);
        double maxActualLineVel = std::get<1>(velocity_data);
        std::vector<double> axisActualVel = std::get<2>(velocity_data);
        std::vector<double> maxAxisActualVel = std::get<3>(velocity_data);
        
        // 4. 计算笛卡尔速度
        calculateCartesianVelocities(actualLineVel, axisActualVel, time, period);

        // 5. 计算笛卡尔加速度
        calculateCartesianAccelerations(time, period);

        // 6. 计算关节速度（保留原有逻辑作为备份）
        calculateJointVelocities(current_positions, time, period);

        // 7. 设置关节力矩占位符
        setJointTorquePlaceholder();

        // 8. 更新时间戳
        robot_state_ros_.time = franka::Duration(time.toSec());
        
    } catch (const std::exception& e) {
        ROS_ERROR("FrankaHW: Error reading robot state: %s", e.what());
    }
}

void FrankaHW::calculateJointVelocities(const std::array<double, 7>& current_positions,
                                       const ros::Time& current_time,
                                       const ros::Duration& period)
{
    if (first_read_) {
        // 首次读取，初始化
        previous_joint_positions_ = current_positions;
        previous_read_time_ = current_time;
        first_read_ = false;
        
        // 初始化速度为零
        robot_state_ros_.dq.fill(0.0);
        filtered_velocities_.fill(0.0);
        
        ROS_INFO("FrankaHW: Initialized joint velocity calculation");
        return;
    }
    
    // 计算时间差
    double dt = (current_time - previous_read_time_).toSec();
    
    // 防止除零或异常时间差
    if (dt <= 0.0 || dt > 0.1) {  // 最大允许100ms的时间间隔
        ROS_WARN_STREAM("FrankaHW: Invalid time delta: " << dt << "s, skipping velocity calculation");
        return;
    }
    
    // 计算原始速度
    std::array<double, 7> raw_velocities;
    for (size_t i = 0; i < 7; ++i) {
        raw_velocities[i] = (current_positions[i] - previous_joint_positions_[i]) / dt;
    }
    
    // 应用低通滤波器平滑速度
    for (size_t i = 0; i < 7; ++i) {
        filtered_velocities_[i] = velocity_filter_alpha_ * raw_velocities[i] + 
                                (1.0 - velocity_filter_alpha_) * filtered_velocities_[i];
        robot_state_ros_.dq[i] = filtered_velocities_[i];
    }
    
    // 更新上一次的值
    previous_joint_positions_ = current_positions;
    previous_read_time_ = current_time;
}

void FrankaHW::setJointTorquePlaceholder()
{
    // 暂时设置为零，为未来的SDK扩展做准备
    // TODO: 当TCB710 SDK支持力矩读取时，在此处实现真实的力矩读取
    robot_state_ros_.tau_J.fill(0.0);
    robot_state_ros_.tau_ext_hat_filtered.fill(0.0);
    
    // 设置其他相关的力矩字段为占位符值
    robot_state_ros_.tau_J_d.fill(0.0);  // 期望力矩
    robot_state_ros_.dtau_J.fill(0.0);   // 力矩导数
}

void FrankaHW::write(const ros::Time& /*time*/, const ros::Duration& /*period*/)
{
    std::lock_guard<std::mutex> ros_lock(ros_cmd_mutex_);
    std::lock_guard<std::mutex> libfranka_lock(libfranka_cmd_mutex_);
    pose_cartesian_command_libfranka_ = pose_cartesian_command_ros_;
    velocity_cartesian_command_libfranka_ = velocity_cartesian_command_ros_;
    effort_joint_command_libfranka_ = effort_joint_command_ros_;
    position_joint_command_libfranka_ = position_joint_command_ros_;
    velocity_joint_command_libfranka_ = velocity_joint_command_ros_;
    run_function_();
}

void FrankaHW::setupJointStateInterface(franka::RobotState& robot_state) {
  for (size_t i = 0; i < joint_names_.size(); i++) {
    hardware_interface::JointStateHandle joint_handle_q(joint_names_[i], &robot_state.q[i],
                                                        &robot_state.dq[i], &robot_state.tau_J[i]);
    joint_state_interface_.registerHandle(joint_handle_q);
  }
  registerInterface(&joint_state_interface_);
}

void FrankaHW::setupFrankaStateInterface(franka::RobotState& robot_state) {
  FrankaStateHandle franka_state_handle(arm_id_ + "_robot", robot_state);
  franka_state_interface_.registerHandle(franka_state_handle);
  registerInterface(&franka_state_interface_);
}

void FrankaHW::setupFrankaCartesianPoseInterface(franka::CartesianPose& pose_cartesian_command) {
  FrankaCartesianPoseHandle franka_cartesian_pose_handle(
      franka_state_interface_.getHandle(arm_id_ + "_robot"), pose_cartesian_command.O_T_EE,
      pose_cartesian_command.elbow);
  franka_pose_cartesian_interface_.registerHandle(franka_cartesian_pose_handle);
  registerInterface(&franka_pose_cartesian_interface_);
}

void FrankaHW::setupFrankaCartesianVelocityInterface(
    franka::CartesianVelocities& velocity_cartesian_command) {
  FrankaCartesianVelocityHandle franka_cartesian_velocity_handle(
      franka_state_interface_.getHandle(arm_id_ + "_robot"), velocity_cartesian_command.O_dP_EE,
      velocity_cartesian_command.elbow);
  franka_velocity_cartesian_interface_.registerHandle(franka_cartesian_velocity_handle);
  registerInterface(&franka_velocity_cartesian_interface_);
}

void FrankaHW::setupFrankaModelInterface(franka::RobotState& robot_state) {
  if (model_) {
    franka_hw::FrankaModelHandle model_handle(arm_id_ + "_model", *model_, robot_state);
    franka_model_interface_.registerHandle(model_handle);
    registerInterface(&franka_model_interface_);
  }
}

void FrankaHW::setupCartesianStateInterface(franka::RobotState& robot_state) {
  CartesianStateHandle cartesian_state_handle(
      arm_id_ + "_cartesian_state",
      robot_state.O_T_EE,
      cartesian_velocity_,
      cartesian_acceleration_);
  cartesian_state_interface_.registerHandle(cartesian_state_handle);
  registerInterface(&cartesian_state_interface_);
}

bool FrankaHW::setRunFunction(const ControlMode& requested_control_mode,
                              const bool limit_rate,
                              const double cutoff_frequency,
                              const franka::ControllerMode internal_controller) {
  using std::placeholders::_1;
  using std::placeholders::_2;
  using Callback = std::function<bool(const franka::RobotState&, franka::Duration)>;

  switch (requested_control_mode) {
    case ControlMode::None:
      break;
    // case ControlMode::JointTorque:
    //   run_function_ = [=](franka::Robot& robot, Callback ros_callback) {
    //     robot.control(std::bind(&FrankaHW::controlCallback<franka::Torques>, this,
    //                             std::cref(effort_joint_command_libfranka_), ros_callback, _1, _2),
    //                   limit_rate, cutoff_frequency);
    //   };
    //   break;
    case ControlMode::JointPosition:
    {
        run_function_ = [=]() {
            // 当前伺服开关关闭
            if (!robot_servo_switch_)
            {
                robot_servo_switch_ = tlibot_->setServoPointMotionControl("true", robot_id_);
                if (!robot_servo_switch_)
                {
                    ROS_ERROR("[%d] Failed to switch to servo point motion control", robot_id_);
                    return;
                }
                std::vector<tcb710_sdk::JointAngles> posVec = {position_joint_command_libfranka_.q};
                tlibot_->servoPointMotionControl(0, 1, 1, posVec, robot_id_);
            }
        };
        break;

    }
    case ControlMode::JointVelocity:
    {
        run_function_ = [=]() {
            // 1. 确保伺服已开启
            if (!robot_servo_switch_) {
                // 尝试开启伺服
                robot_servo_switch_ = !tlibot_->setServoPointMotionControl("true", robot_id_);
                if (!robot_servo_switch_) {
                    ROS_ERROR("[%d] Failed to start servo point motion control.", robot_id_);
                    return;
                } else {
                    ROS_INFO("[%d] Servo motion control started successfully.", robot_id_);
                    // 伺服刚启动时，重置时间，避免dt过大
                    last_command_time_ = ros::Time::now();
                    // 并且将当前位置作为第一个目标，防止瞬间跳变
                    last_commanded_position_ = robot_state_ros_.q;
                    return; // 第一次启动时只开启伺服并初始化，不立即发送命令
                }
            }
            // 2. 获取时间差 dt
            ros::Time current_time = ros::Time::now();
            ros::Duration dt_duration = current_time - last_command_time_;
            double dt = dt_duration.toSec();
            // 鲁棒性检查：防止 dt 过小或过大
            if (dt <= 0.0 || dt > 0.1) { // 限制最大dt为100ms
                ROS_WARN_STREAM("Invalid or too large dt: " << dt << "s. Resetting last_command_time_.");
                last_command_time_ = current_time;
                return; // 跳过本次更新，等待下一个有效周期
            }
            last_command_time_ = current_time; // 更新上次命令时间
            // 3. 获取机器人当前状态和期望速度
            franka::JointVelocities desired_dq = velocity_joint_command_libfranka_.dq;
            // 4. 计算新的目标位置 (速度积分)
            tcb710_sdk::JointAngles new_target_position;
            for (int i = 0; i < desired_dq.dq.size(); ++i) {
                // 使用上一个发送的命令位置进行积分，确保目标轨迹平滑
                double delta_q = desired_dq.dq[i] * dt;
                new_target_position[i] = last_commanded_position_[i] + delta_q;
            }
            // 5. 将新计算的目标位置发送给底层的位置伺服接口
            std::vector<tcb710_sdk::JointAngles> posVec = {new_target_position};
            tlibot_->servoPointMotionControl(0, 1, 1, posVec, robot_id_);
            // 6. 更新上一个命令的位置，为下一次计算做准备
            last_commanded_position_ = new_target_position;
            
            ROS_DEBUG_STREAM("Robot " << robot_id_ << " dt: " << dt << "s, Desired dq: " << desired_dq.dq[0] 
                            << ", New target q: " << new_target_position[0] << ", Current actual q: " << robot_state_ros_.q[0]);
        };
        break;
    }
    case ControlMode::CartesianPose:
    {
        run_function_ = [=]() {
            // 当前伺服开关关闭
            if (robot_servo_switch_)
            {
                robot_servo_switch_ = tlibot_->setServoPointMotionControl("false", robot_id_);
                if (robot_servo_switch_)
                {
                    ROS_ERROR("[%d] Failed to stop servo point motion control", robot_id_);
                    return;
                }
            }
            tcb710_sdk::JointAngles pos = transformArrayToPose(pose_cartesian_command_libfranka_.O_T_EE);
            tlibot_->movl(1, tcb710_sdk::CoordinateType::CART, pos, robot_id_);
        };
        break;
    }
    // case ControlMode::CartesianVelocity:
    //   run_function_ = [=](franka::Robot& robot, Callback ros_callback) {
    //     robot.control(
    //         std::bind(&FrankaHW::controlCallback<franka::CartesianVelocities>, this,
    //                   std::cref(velocity_cartesian_command_libfranka_), ros_callback, _1, _2),
    //         internal_controller, limit_rate, cutoff_frequency);
    //   };
    //   break;
    // case (ControlMode::JointTorque | ControlMode::JointPosition):
    //   run_function_ = [=](franka::Robot& robot, Callback ros_callback) {
    //     robot.control(std::bind(&FrankaHW::controlCallback<franka::Torques>, this,
    //                             std::cref(effort_joint_command_libfranka_), ros_callback, _1, _2),
    //                   std::bind(&FrankaHW::controlCallback<franka::JointPositions>, this,
    //                             std::cref(position_joint_command_libfranka_), ros_callback, _1, _2),
    //                   limit_rate, cutoff_frequency);
    //   };
    //   break;
    // case (ControlMode::JointTorque | ControlMode::JointVelocity):
    //   run_function_ = [=](franka::Robot& robot, Callback ros_callback) {
    //     robot.control(std::bind(&FrankaHW::controlCallback<franka::Torques>, this,
    //                             std::cref(effort_joint_command_libfranka_), ros_callback, _1, _2),
    //                   std::bind(&FrankaHW::controlCallback<franka::JointVelocities>, this,
    //                             std::cref(velocity_joint_command_libfranka_), ros_callback, _1, _2),
    //                   limit_rate, cutoff_frequency);
    //   };
    //   break;
    // case (ControlMode::JointTorque | ControlMode::CartesianPose):
    //   run_function_ = [=](franka::Robot& robot, Callback ros_callback) {
    //     robot.control(std::bind(&FrankaHW::controlCallback<franka::Torques>, this,
    //                             std::cref(effort_joint_command_libfranka_), ros_callback, _1, _2),
    //                   std::bind(&FrankaHW::controlCallback<franka::CartesianPose>, this,
    //                             std::cref(pose_cartesian_command_libfranka_), ros_callback, _1, _2),
    //                   limit_rate, cutoff_frequency);
    //   };
    //   break;
    // case (ControlMode::JointTorque | ControlMode::CartesianVelocity):
    //   run_function_ = [=](franka::Robot& robot, Callback ros_callback) {
    //     robot.control(
    //         std::bind(&FrankaHW::controlCallback<franka::Torques>, this,
    //                   std::cref(effort_joint_command_libfranka_), ros_callback, _1, _2),
    //         std::bind(&FrankaHW::controlCallback<franka::CartesianVelocities>, this,
    //                   std::cref(velocity_cartesian_command_libfranka_), ros_callback, _1, _2),
    //         limit_rate, cutoff_frequency);
    //   };
    //   break;
    default:
      ROS_WARN("FrankaHW: No valid control mode selected; cannot switch controllers.");
      return false;
  }
  return true;
}

void FrankaHW::initROSInterfaces(ros::NodeHandle& /*robot_hw_nh*/) {
  setupJointStateInterface(robot_state_ros_);
  setupJointCommandInterface(position_joint_command_ros_.q, robot_state_ros_, true,
                             position_joint_interface_);
  setupJointCommandInterface(velocity_joint_command_ros_.dq, robot_state_ros_, true,
                             velocity_joint_interface_);
  setupJointCommandInterface(effort_joint_command_ros_.tau_J, robot_state_ros_, false,
                             effort_joint_interface_);
  setupLimitInterface<joint_limits_interface::PositionJointSoftLimitsHandle>(
      position_joint_limit_interface_, position_joint_interface_);
  setupLimitInterface<joint_limits_interface::VelocityJointSoftLimitsHandle>(
      velocity_joint_limit_interface_, velocity_joint_interface_);
  setupLimitInterface<joint_limits_interface::EffortJointSoftLimitsHandle>(
      effort_joint_limit_interface_, effort_joint_interface_);
  setupFrankaStateInterface(robot_state_ros_);
  setupFrankaCartesianPoseInterface(pose_cartesian_command_ros_);
  setupFrankaCartesianVelocityInterface(velocity_cartesian_command_ros_);
  setupCartesianStateInterface(robot_state_ros_);
  setupFrankaModelInterface(robot_state_ros_);
}

void FrankaHW::initRobot() {
  connect();
  // 注意：TCB710 SDK不支持动力学模型，这里暂时跳过模型初始化
  // model_ = std::make_unique<franka_hw::Model>(robot_->loadModel());
  // update(robot_->readOnce());

  model_ = std::make_unique<tcb710_sdk::ModelSdk>();
  
  // 初始化机器人状态结构体的默认值
  // 由于TCB710 SDK的限制，许多字段需要设置为默认值
  std::fill(robot_state_ros_.q.begin(), robot_state_ros_.q.end(), 0.0);
  std::fill(robot_state_ros_.dq.begin(), robot_state_ros_.dq.end(), 0.0);
  std::fill(robot_state_ros_.tau_J.begin(), robot_state_ros_.tau_J.end(), 0.0);
  
  // 初始化变换矩阵为单位矩阵
  robot_state_ros_.O_T_EE = {1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1};
  
  ROS_INFO("FrankaHW: TCB710 robot initialized successfully");
}

void FrankaHW::setupParameterCallbacks(ros::NodeHandle& robot_hw_nh) {
  get_limit_rate_ = [robot_hw_nh]() {
    bool rate_limiting;
    robot_hw_nh.getParamCached("rate_limiting", rate_limiting);
    return rate_limiting;
  };

  get_internal_controller_ = [robot_hw_nh]() {
    std::string internal_controller;
    robot_hw_nh.getParamCached("internal_controller", internal_controller);
    franka::ControllerMode controller_mode;
    if (internal_controller == "joint_impedance") {
      controller_mode = franka::ControllerMode::kJointImpedance;
    } else if (internal_controller == "cartesian_impedance") {
      controller_mode = franka::ControllerMode::kCartesianImpedance;
    } else {
      ROS_WARN("Invalid internal_controller parameter provided, falling back to joint impedance");
      controller_mode = franka::ControllerMode::kJointImpedance;
    }
    return controller_mode;
  };

  get_cutoff_frequency_ = [robot_hw_nh]() {
    double cutoff_frequency;
    robot_hw_nh.getParamCached("cutoff_frequency", cutoff_frequency);
    return cutoff_frequency;
  };
}

bool FrankaHW::commandHasNaN(const franka::Torques& command) {
  return arrayHasNaN(command.tau_J);
}

bool FrankaHW::commandHasNaN(const franka::JointPositions& command) {
  return arrayHasNaN(command.q);
}

bool FrankaHW::commandHasNaN(const franka::JointVelocities& command) {
  return arrayHasNaN(command.dq);
}

bool FrankaHW::commandHasNaN(const franka::CartesianPose& command) {
  return arrayHasNaN(command.elbow) || arrayHasNaN(command.O_T_EE);
}

bool FrankaHW::commandHasNaN(const franka::CartesianVelocities& command) {
  return arrayHasNaN(command.elbow) || arrayHasNaN(command.O_dP_EE);
}

std::vector<double> FrankaHW::getCollisionThresholds(const std::string& name,
                                                     const ros::NodeHandle& robot_hw_nh,
                                                     const std::vector<double>& defaults) {
  std::vector<double> thresholds;
  if (!robot_hw_nh.getParam("collision_config/" + name, thresholds) ||
      thresholds.size() != defaults.size()) {
    std::string message;
    for (const double& threshold : defaults) {
      message += std::to_string(threshold);
      message += " ";
    }
    ROS_INFO("No parameter %s found, using default values: %s", name.c_str(), message.c_str());
    return defaults;
  }
  return thresholds;
}

void FrankaHW::calculateCartesianVelocities(double actualLineVel,
                                           const std::vector<double>& axisActualVel,
                                           const ros::Time& current_time,
                                           const ros::Duration& period) {
    if (first_cartesian_read_) {
        // 首次读取，初始化
        cartesian_velocity_.fill(0.0);
        previous_cartesian_velocity_.fill(0.0);
        previous_cartesian_time_ = current_time;
        first_cartesian_read_ = false;
        ROS_INFO("FrankaHW: Initialized Cartesian velocity calculation");
        return;
    }

    // 线速度：直接使用SDK返回值，转换单位 mm/s -> m/s
    // 注意：actualLineVel是末端的合成线速度，这里简化处理
    cartesian_velocity_[0] = actualLineVel / 1000.0;  // 简化：假设主要在X方向
    cartesian_velocity_[1] = 0.0;  // 需要更复杂的分解算法
    cartesian_velocity_[2] = 0.0;  // 需要更复杂的分解算法

    // 角速度：通过雅可比矩阵从关节角速度计算
    if (axisActualVel.size() >= 7) {
        // 使用机器人运动学计算末端角速度
        // omega = J_angular * dq
        calculateEndEffectorAngularVelocity(axisActualVel);
    }

    // 更新机器人状态
    robot_state_ros_.O_dP_EE_d[0] = cartesian_velocity_[0];  // vx
    robot_state_ros_.O_dP_EE_d[1] = cartesian_velocity_[1];  // vy
    robot_state_ros_.O_dP_EE_d[2] = cartesian_velocity_[2];  // vz
    robot_state_ros_.O_dP_EE_d[3] = cartesian_velocity_[3];  // wx
    robot_state_ros_.O_dP_EE_d[4] = cartesian_velocity_[4];  // wy
    robot_state_ros_.O_dP_EE_d[5] = cartesian_velocity_[5];  // wz

    // 更新上一次的值
    previous_cartesian_velocity_ = cartesian_velocity_;
    previous_cartesian_time_ = current_time;
}

void FrankaHW::calculateCartesianAccelerations(const ros::Time& current_time,
                                              const ros::Duration& period) {
    if (first_cartesian_read_) {
        cartesian_acceleration_.fill(0.0);
        return;
    }

    double dt = (current_time - previous_cartesian_time_).toSec();
    if (dt <= 0.0 || dt > 0.1) {
        ROS_WARN_STREAM("FrankaHW: Invalid time delta for acceleration: " << dt << "s");
        return;
    }

    // 计算加速度（数值微分）
    for (size_t i = 0; i < 6; ++i) {
        cartesian_acceleration_[i] = (cartesian_velocity_[i] - previous_cartesian_velocity_[i]) / dt;
    }
}

void FrankaHW::calculateEndEffectorAngularVelocity(const std::vector<double>& axisActualVel) {
    // 简化实现：这里需要使用机器人的雅可比矩阵
    // 实际应该是: omega = J_angular * dq
    // 目前简化为零，待后续完善
    cartesian_velocity_[3] = 0.0;  // wx
    cartesian_velocity_[4] = 0.0;  // wy
    cartesian_velocity_[5] = 0.0;  // wz

    // TODO: 实现真正的雅可比矩阵计算
    // 需要机器人的DH参数和当前关节角度
    ROS_DEBUG("FrankaHW: Angular velocity calculation simplified - needs Jacobian implementation");
}

}  // namespace franka_hw
