#include "tcb710_sdk/model_sdk.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <thread>
#include <algorithm>
#include <cstring>

#include <ros/console.h>

#include <Python.h>
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <numpy/arrayobject.h>

#define Py_DECREF_CHECK(p) \
    {                      \
        if (NULL != p)     \
        {                  \
            Py_DECREF(p);  \
        }                  \
    }

void *call_import_array()
{
    import_array();
    return NULL;
}

namespace tcb710_sdk
{

    ModelSdk::ModelSdk()
    {
        ROS_INFO("ModelSdk()");
        m_Init = false;
        Py_Initialize();
        call_import_array();
        // 检查初始化是否成功
        if (!Py_IsInitialized())
        {
            ROS_ERROR("Py_Initialize error");
        }
        PyRun_SimpleString("import sys,os");
        PyRun_SimpleString("print(os.getcwd())");
        PyRun_SimpleString("print(sys.version)");

        //TODO python脚本路径怎么定
        PyRun_SimpleString("sys.path.append('/home/<USER>/tlibot_ws/src/ssp-node/tlibot_ros/franka_hw/lib')");
        PyRun_SimpleString("print(sys.path)");

        // 载入名为tlpy310的脚本
        m_pName = (void *)PyUnicode_FromString("tlpy310");
        m_pModule = (void *)PyImport_Import((PyObject *)m_pName);
        if (!m_pModule)
        {
            ROS_ERROR("Import tlpy310 error");
        }

        m_pDict = (void *)PyModule_GetDict((PyObject *)m_pModule);
        if (!m_pDict)
        {
            ROS_ERROR("GetDict error");
        }

        m_pFuncFkine = (void *)PyDict_GetItemString((PyObject *)m_pDict, "call_fkine");
        if (!m_pFuncFkine || !PyCallable_Check((PyObject *)m_pFuncFkine))
        {
            ROS_ERROR("can't find function [call_fkine]");
        }

        m_pFuncJacobian = (void *)PyDict_GetItemString((PyObject *)m_pDict, "call_jacobian");
        if (!m_pFuncJacobian || !PyCallable_Check((PyObject *)m_pFuncJacobian))
        {
            ROS_ERROR("can't find function [call_jacobian]");
        }

        ROS_INFO("load tlpy310 success");
        m_Init = true;

        //test
        // franka::Frame frame;
        // std::array<double, 7> q = {10, 20, 30, 40, 30, 20, 10};
        // std::array<double, 16> F_T_EE;
        // std::array<double, 16> EE_T_K;
        // pose(frame, q, F_T_EE, EE_T_K);
        // bodyJacobian(frame, q, F_T_EE, EE_T_K);
    }

    ModelSdk::~ModelSdk() 
    {
        ROS_INFO("~ModelSdk()");
        Py_DECREF_CHECK(m_pName);
        Py_DECREF_CHECK(m_pModule);
        Py_DECREF_CHECK(m_pDict);
        Py_DECREF_CHECK(m_pFuncFkine);
        Py_DECREF_CHECK(m_pFuncJacobian);
    }

    PyArrayObject *callPyFunc(PyObject *pyFunc, const std::array<double, 7> &q)
    {
        if (!pyFunc)
        {
            ROS_ERROR("Python function pointer is null");
            return nullptr;
        }

        /* 复制一份数据，因为 PyArray_SimpleNewFromData 需要非 const 指针 */
        std::array<double, 7> qTemp = q;

        npy_intp dims[1] = {static_cast<npy_intp>(qTemp.size())};
        PyObject *pyArr = PyArray_SimpleNewFromData(1, dims, NPY_DOUBLE, qTemp.data());
        if (!pyArr)
        {
            ROS_ERROR("failed to create numpy array");
            return nullptr;
        }

        PyObject *pyArgs = PyTuple_Pack(1, pyArr); // PyTuple_New + SetItem 的简写
        if (!pyArgs)
        {
            Py_DECREF(pyArr);
            return nullptr;
        }

        PyObject *pyRet = PyObject_CallObject(pyFunc, pyArgs);
        Py_DECREF(pyArgs);
        Py_DECREF(pyArr);

        if (!pyRet)
        {
            ROS_ERROR("Python call failed");
            return nullptr;
        }

        if (!PyArray_Check(pyRet) || PyArray_TYPE(reinterpret_cast<PyArrayObject *>(pyRet)) != NPY_DOUBLE)
        {
            ROS_ERROR("Python did not return a double numpy array");
            Py_DECREF(pyRet);
            return nullptr;
        }

        return reinterpret_cast<PyArrayObject *>(pyRet);
    }

    std::array<double, 16> ModelSdk::pose(
        franka::Frame frame,
        const std::array<double, 7> &q,
        const std::array<double, 16> &F_T_EE, // NOLINT(readability-identifier-naming)
        const std::array<double, 16> &EE_T_K) // NOLINT(readability-identifier-naming)
        const
    {
        std::array<double, 16> ret{};
        if (!m_Init)
        {
            ROS_ERROR("init failed");
            return ret;
        }

        PyArrayObject *pyOut = callPyFunc((PyObject*)m_pFuncFkine, q);
        if (!pyOut)
            return ret;

        if (PyArray_SIZE(pyOut) != 16)
        {
            ROS_ERROR("pose: expected 16 values, got %ld", PyArray_SIZE(pyOut));
        }
        else
        {
            std::copy_n(static_cast<double *>(PyArray_DATA(pyOut)), 16, ret.begin());
        }
        Py_DECREF(pyOut);
        return ret;
 }

    std::array<double, 42> ModelSdk::bodyJacobian(
        franka::Frame frame,
        const std::array<double, 7> &q,
        const std::array<double, 16> &F_T_EE, // NOLINT(readability-identifier-naming)
        const std::array<double, 16> &EE_T_K) // NOLINT(readability-identifier-naming)
        const
    {
        std::array<double, 42> ret{};
        if (!m_Init)
        {
            ROS_ERROR("init failed");
            return ret;
        }

        PyArrayObject *pyOut = callPyFunc((PyObject*)m_pFuncJacobian, q);
        if (!pyOut)
            return ret;

        if (PyArray_SIZE(pyOut) != 42)
        {
            ROS_ERROR("bodyJacobian: expected 42 values, got %ld", PyArray_SIZE(pyOut));
        }
        else
        {
            std::copy_n(static_cast<double *>(PyArray_DATA(pyOut)), 42, ret.begin());
        }
        Py_DECREF(pyOut);
        return ret;
    }

    std::array<double, 42> ModelSdk::zeroJacobian(
        franka::Frame frame,
        const std::array<double, 7> &q,
        const std::array<double, 16> &F_T_EE, // NOLINT(readability-identifier-naming)
        const std::array<double, 16> &EE_T_K) // NOLINT(readability-identifier-naming)
        const
    {
        // TODO
        std::array<double, 42> ret;
        return ret;
    }

    std::array<double, 49> ModelSdk::mass(
        const std::array<double, 7> &q,
        const std::array<double, 9> &I_total, // NOLINT(readability-identifier-naming)
        double m_total,
        const std::array<double, 3> &F_x_Ctotal) // NOLINT(readability-identifier-naming)
        const
    {
        // TODO
        std::array<double, 49> ret;
        return ret;
    }

    std::array<double, 7> ModelSdk::coriolis(
        const std::array<double, 7> &q,
        const std::array<double, 7> &dq,
        const std::array<double, 9> &I_total, // NOLINT(readability-identifier-naming)
        double m_total,
        const std::array<double, 3> &F_x_Ctotal) // NOLINT(readability-identifier-naming)
        const
    {
        // TODO
        std::array<double, 7> ret;
        return ret;
    }

    std::array<double, 7> ModelSdk::gravity(
        const std::array<double, 7> &q,
        double m_total,
        const std::array<double, 3> &F_x_Ctotal, // NOLINT(readability-identifier-naming)
        const std::array<double, 3> &gravity_earth)
        const
    {
        // TODO
        std::array<double, 7> ret;
        return ret;
    }
} // namespace tcb710_sdk
