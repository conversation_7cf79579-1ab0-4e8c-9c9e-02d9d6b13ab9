cmake_minimum_required(VERSION 3.4)
project(ft_sensor_node)

# 检查ROS serial功能包是否安装
find_package(catkin QUIET COMPONENTS serial)
if(NOT catkin_FOUND OR NOT serial_FOUND)
    message(FATAL_ERROR "ROS serial package not found! Please install it first:")
    message(FATAL_ERROR "  Ubuntu/Debian: sudo apt-get install ros-noetic-serial")
    message(FATAL_ERROR "  Or install from source: https://github.com/wjwwood/serial")
    message(FATAL_ERROR "Build stopped due to missing ROS serial package.")
endif()

find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  geometry_msgs
  message_generation
  realtime_tools
  serial
)

add_service_files(FILES GetDebugStats.srv)

generate_messages(
  DEPENDENCIES
  std_msgs
  geometry_msgs
)

catkin_package(
  CATKIN_DEPENDS message_runtime std_msgs geometry_msgs
)

include_directories(
  include
  ${catkin_INCLUDE_DIRS}
)

add_executable(ft_sensor_node src/ft_sensor_node.cpp)
target_link_libraries(ft_sensor_node
  ${catkin_LIBRARIES}
)

add_dependencies(ft_sensor_node ${catkin_EXPORTED_TARGETS} ${${PROJECT_NAME}_EXPORTED_TARGETS}) 