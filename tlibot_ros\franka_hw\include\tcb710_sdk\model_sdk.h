#pragma once

#include <array>

#include <franka/model.h>
#include <franka_hw/model_base.h>
#include "tcb710_sdk/robotarm_sdk.h"

namespace tcb710_sdk
{
    class ModelSdk : public franka_hw::ModelBase
    {
    public:
        ModelSdk();
        ~ModelSdk();
        std::array<double, 16> pose(
            franka::Frame frame,
            const std::array<double, 7> &q,
            const std::array<double, 16> &F_T_EE, // NOLINT(readability-identifier-naming)
            const std::array<double, 16> &EE_T_K) // NOLINT(readability-identifier-naming)
            const override;

        std::array<double, 42> bodyJacobian(
            franka::Frame frame,
            const std::array<double, 7> &q,
            const std::array<double, 16> &F_T_EE, // NOLINT(readability-identifier-naming)
            const std::array<double, 16> &EE_T_K) // NOLINT(readability-identifier-naming)
            const override;

        std::array<double, 42> zeroJacobian(
            franka::Frame frame,
            const std::array<double, 7> &q,
            const std::array<double, 16> &F_T_EE, // NOLINT(readability-identifier-naming)
            const std::array<double, 16> &EE_T_K) // NOLINT(readability-identifier-naming)
            const override;

        std::array<double, 49> mass(
            const std::array<double, 7> &q,
            const std::array<double, 9> &I_total, // NOLINT(readability-identifier-naming)
            double m_total,
            const std::array<double, 3> &F_x_Ctotal) // NOLINT(readability-identifier-naming)
            const override;

        std::array<double, 7> coriolis(
            const std::array<double, 7> &q,
            const std::array<double, 7> &dq,
            const std::array<double, 9> &I_total, // NOLINT(readability-identifier-naming)
            double m_total,
            const std::array<double, 3> &F_x_Ctotal) // NOLINT(readability-identifier-naming)
            const override;

        std::array<double, 7> gravity(
            const std::array<double, 7> &q,
            double m_total,
            const std::array<double, 3> &F_x_Ctotal, // NOLINT(readability-identifier-naming)
            const std::array<double, 3> &gravity_earth)
            const override;

    private:
        bool m_Init;
        void *m_pName, *m_pModule, *m_pDict;
        void *m_pFuncFkine;
        void *m_pFuncJacobian;
    };

} // namespace franka_hw
