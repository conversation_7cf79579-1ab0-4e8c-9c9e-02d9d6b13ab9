# tl_kinematics
计算雅各比矩阵,替换原有的Model类
天链提供的python库是依赖python3.10的, 都是在ros2开发的, pinocchio库也是基于python3.10版本的
当前ubuntu20通过apt install robotpkg-py3*-pinocchio安装的只有python3.8的

## 安装python3.10
ubuntu20的源没有python3.10
手动下载python3.10安装包:https://www.python.org/downloads/release/python-31017/
./configure --prefix=/opt/python3.10  --enable-shared  --enable-optimizations
make
make install

## 安装pinocchio动力学库
/opt/python3.10/bin/pip3 install pin -i https://artnj.zte.com.cn/artifactory/api/pypi/public-pypi-virtual/simple
