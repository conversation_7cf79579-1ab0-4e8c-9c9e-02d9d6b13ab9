import numpy as np
import os, sys
import tl_kinematics

def call_fkine(arr: np.ndarray) -> np.ndarray:
    print ("in python function call_fkine")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    urdf_path = os.path.abspath(os.path.join(current_dir, f'./fdr2.urdf'))
    joint_id  = "joint7"
    robot = tl_kinematics.RobotKinematics(urdf_path, joint_id)
    T1 = robot.fkine(arr)
    return T1

def call_jacobian(arr: np.ndarray) -> np.ndarray:
    print ("in python function call_jacobian")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    urdf_path = os.path.abspath(os.path.join(current_dir, f'./fdr2.urdf'))
    joint_id  = "joint7"
    robot = tl_kinematics.RobotKinematics(urdf_path, joint_id)
    
    # """雅可比矩阵计算"""
    # J = robot.jacobian(arr)
    # print(f"计算得出的雅可比矩阵: \n{J}\n")
    
    
    # n = arr.size
    # jac = np.zeros((n, n))
    # for i in range(n):
    #     # 此处为简化示例，实际需替换为你的偏导数计算逻辑
    #     jac[i, i] = 2 * arr[i]  # 示例：对角矩阵
    return J

    
