// Copyright (c) 2017 Franka Emika GmbH
// Use of this source code is governed by the Apache-2.0 license, see LICENSE
#pragma once

#include <cstdint>
#include <memory>
#include <string>

#include <ros/node_handle.h>

#include <franka/gripper_state.h>
#include <franka/duration.h>

#define MaxWidth        0.04                //最大距离40mm
#define MinTorque       40.0                //最小40N
#define MaxTorque       100.0               //最大100N
#define MaxSpeedTime    1.1                 //打开闭合时间1.1s
#define MaxSpeed (MaxWidth/MaxSpeedTime)    //最大速度


enum class GripperErrorCode : int32_t {
    SUCCESS = 0,
    INIT_FAILED = -1,
    STATUS_ERROR = -2,
    POSITION_ERROR = -3,
    SPEED_ERROR = -4,
    TORQUE_ERROR = -5,
    TEMPERATURE_ERROR = -6
};

// 错误收集器
class ErrorCollector {
public:
    void addError(int32_t error_code) {
        if (error_code != 0) {
            error_codes_.push_back(error_code);
        }
    }
    
    void clear() {
        error_codes_.clear();
    }
    
    bool hasErrors() const {
        return !error_codes_.empty();
    }
    
    const std::vector<int32_t>& getErrorCodes() const {
        return error_codes_;
    }
    
    int32_t getErrorCount() const {
        return static_cast<int32_t>(error_codes_.size());
    }

    // 获取组合的错误描述
    std::string getCombinedDescription() const
    {
        if (error_codes_.empty())
            return "SUCCESS";

        static const std::pair<int32_t, const char*> kMap[] = {
            {static_cast<int32_t>(GripperErrorCode::INIT_FAILED),       "INIT_FAILED"},
            {static_cast<int32_t>(GripperErrorCode::STATUS_ERROR),      "STATUS_ERROR"},
            {static_cast<int32_t>(GripperErrorCode::POSITION_ERROR),    "POSITION_ERROR"},
            {static_cast<int32_t>(GripperErrorCode::SPEED_ERROR),       "SPEED_ERROR"},
            {static_cast<int32_t>(GripperErrorCode::TORQUE_ERROR),      "TORQUE_ERROR"},
            {static_cast<int32_t>(GripperErrorCode::TEMPERATURE_ERROR), "TEMPERATURE_ERROR"},
        };
        const size_t kMapSize = sizeof(kMap) / sizeof(kMap[0]);
        std::string result = "ERRORS: ";

        for (size_t i = 0; i < error_codes_.size(); ++i)
        {
            if (i > 0)result += ", ";
            int32_t code = error_codes_[i];
            const std::pair<int32_t, const char *> *it =
                std::find_if(kMap, kMap + kMapSize,
                             [code](const std::pair<int32_t, const char *> &p)
                             {
                                 return p.first == code;
                             });

            if (it != kMap + kMapSize)
                result += it->second;
            else
                result += "UNKNOWN_ERROR(" + std::to_string(error_codes_[i]) + ")";
        }
        return result;
    }
    
private:
    std::vector<int32_t> error_codes_;
};

/**
 * @file jodellgripper.h
 * Contains the jodell::Gripper type.
 */

namespace jodell {

  struct GripperState {
  /**
   * Current gripper opening width. Unit: \f$[m]\f$.
   */
  double width{};

  /**
   * Maximum gripper opening width.
   * This parameter is estimated by homing the gripper.
   * After changing the gripper fingers, a homing needs to be done. Unit: \f$[m]\f$.
   *
   * @see Gripper::homing.
   */
  double max_width{};

  /**
   * Indicates whether an object is currently grasped.
   */
  bool is_grasped{};

  /**
   * Current gripper temperature. Unit: \f$[°C]\f$.
   */
  uint16_t temperature{};

  /**
   * Strictly monotonically increasing timestamp since robot start.
   */
  franka::Duration time{};

  double speed{};   //夹爪速度
  double torque{};   //夹爪力矩
  
  // 错误收集器，用于收集多个错误码
  ErrorCollector error_collector;
  

  };
//class Network;

/**
 * Maintains a network connection to the gripper, provides the current gripper state,
 * and allows the execution of commands.
 *
 * @note
 * The members of this class are threadsafe.
 */
class Gripper {
 public:
  /**
   * Version of the gripper server.
   */
  using ServerVersion = uint16_t;

  /**
   * Establishes a connection with a gripper connected to a robot.
   *
   * @param[in] franka_address IP/hostname of the robot the gripper is connected to.
   *
   * @throw NetworkException if the connection is unsuccessful.
   * @throw IncompatibleVersionException if this version of `libfranka` is not supported.
   */
  //explicit Gripper(const std::string& franka_address);
  explicit Gripper();

  /**
   * Move-constructs a new Gripper instance.
   *
   * @param[in] gripper Other Gripper instance.
   */
  Gripper(Gripper&& gripper) noexcept;

  /**
   * Move-assigns this Gripper from another Gripper instance.
   *
   * @param[in] gripper Other Gripper instance.
   *
   * @return Model instance.
   */
  Gripper& operator=(Gripper&& gripper) noexcept;

  /**
   * Closes the connection.
   */
  ~Gripper() noexcept;

  int init();

  /**
   * Performs homing of the gripper.
   *
   * After changing the gripper fingers, a homing needs to be done.
   * This is needed to estimate the maximum grasping width.
   *
   * @return True if command was successful, false otherwise.
   *
   * @throw CommandException if an error occurred.
   * @throw NetworkException if the connection is lost, e.g. after a timeout.
   *
   * @see GripperState for the maximum grasping width.
   */
  bool homing() const;

  /**
   * Grasps an object.
   *
   * An object is considered grasped if the distance \f$d\f$ between the gripper fingers satisfies
   * \f$(\text{width} - \text{epsilon_inner}) < d < (\text{width} + \text{epsilon_outer})\f$.
   *
   * @param[in] width Size of the object to grasp. [m]
   * @param[in] speed Closing speed. [m/s]
   * @param[in] force Grasping force. [N]
   * @param[in] epsilon_inner Maximum tolerated deviation when the actual grasped width is smaller
   * than the commanded grasp width.
   * @param[in] epsilon_outer Maximum tolerated deviation when the actual grasped width is larger
   * than the commanded grasp width.
   *
   * @return True if an object has been grasped, false otherwise.
   *
   * @throw CommandException if an error occurred.
   * @throw NetworkException if the connection is lost, e.g. after a timeout.
   */
  bool grasp(double width,
             double speed,
             double force,
             double epsilon_inner = 0.005,
             double epsilon_outer = 0.005) const;

  /**
   * Moves the gripper fingers to a specified width.
   *
   * @param[in] width Intended opening width. [m]
   * @param[in] speed Closing speed. [m/s]
   *
   * @return True if command was successful, false otherwise.
   *
   * @throw CommandException if an error occurred.
   * @throw NetworkException if the connection is lost, e.g. after a timeout.
   */
  bool move(double width, double speed) const;

  /**
   * Stops a currently running gripper move or grasp.
   *
   * @return True if command was successful, false otherwise.
   *
   * @throw CommandException if an error occurred.
   * @throw NetworkException if the connection is lost, e.g. after a timeout.
   */
  bool stop() const;

  /**
   * Waits for a gripper state update and returns it.
   *
   * @return Current gripper state.
   *
   * @throw NetworkException if the connection is lost, e.g. after a timeout.
   * @throw InvalidOperationException if another readOnce is already running.
   */
  jodell::GripperState readOnce() const;

  /**
   * Returns the software version reported by the connected server.
   *
   * @return Software version of the connected server.
   */
  ServerVersion serverVersion() const noexcept;

  /// @cond DO_NOT_DOCUMENT
  Gripper(const Gripper&) = delete;
  Gripper& operator=(const Gripper&) = delete;
  /// @endcond

 private:
  //std::unique_ptr<Network> network_;

  uint16_t ri_version_;

  int slave_id;     //夹爪站点id，默认为9
  int com_index;     //串口编号（0-7）
  bool m_Init;
};



}  // namespace franka
