<?xml version="1.0" ?>
<launch>
  <arg name="robot_ip" />
  <arg name="robot" default="panda" doc="choose your robot. Possible values: [panda, fr3]"/>
  <arg name="arm_id" default="$(arg robot)" />
  <arg name="load_gripper" default="true" />
  <arg name="load_ft_sensor" default="true" />
  <arg name="xacro_args" default="" />

  <param name="robot_description" command="$(find xacro)/xacro $(find franka_description)/robots/$(arg robot)/$(arg robot).urdf.xacro hand:=$(arg load_gripper) arm_id:=$(arg arm_id) $(arg xacro_args)"/>

  <include file="$(find franka_gripper)/launch/franka_gripper.launch" if="$(arg load_gripper)">
    <!-- <arg name="robot_ip" value="$(arg robot_ip)" /> -->
    <arg name="arm_id"   value="$(arg arm_id)" />
  </include>

  <include file="$(find ft_sensor_node)/launch/ft_sensor_node.launch" if="$(arg load_ft_sensor)" >
  </include>

  <node name="franka_control" pkg="franka_control" type="franka_control_node" output="screen" required="true">
    <rosparam command="load" file="$(find franka_control)/config/franka_control_node.yaml" subst_value="true" />
    <param name="robot_ip" value="$(arg robot_ip)" />
  </node>

  <rosparam command="load" file="$(find franka_control)/config/default_controllers.yaml" subst_value="true" />
  <node name="state_controller_spawner" pkg="controller_manager" type="spawner" respawn="false" output="screen" args="franka_state_controller"/>
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" output="screen"/>
  <node name="joint_state_publisher" type="joint_state_publisher" pkg="joint_state_publisher" output="screen">
    <rosparam if="$(arg load_gripper)" param="source_list">[franka_state_controller/joint_states, franka_gripper/joint_states] </rosparam>
    <rosparam unless="$(arg load_gripper)" param="source_list">[franka_state_controller/joint_states] </rosparam>
    <param name="rate" value="30"/>
  </node>
</launch>
